# Get started with Unity Version Control

The Version Control package provides an integration of Unity Version Control (Unity VCS, formerly Plastic SCM) in the Unity Editor.

Unity Version Control enables you to work collaboratively by providing advanced features such as branching, locking, merging, and a standalone Desktop GUI.

Learn more about [Unity Version Control](https://unity.com/solutions/version-control).

* To start with a new version control repository for your project, see [Get started with a new repository](GetStartedNewRepository.md).
* To start from an existing Unity Version Control repository, see [Get started with an existing repository](GetStartedExistingRepository.md).

For more information on how to get started, refer to the [Unity Version Control documentation](https://docs.unity.com/ugs/en-us/manual/devops/manual/unity-version-control).
