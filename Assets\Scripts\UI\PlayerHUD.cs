using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace SoulsLike2D.UI
{
    public class PlayerHUD : MonoBehaviour
    {
        [Header("Health UI")]
        [SerializeField] private Slider healthSlider;
        [SerializeField] private Image healthFill;
        [SerializeField] private TextMeshProUGUI healthText;
        [SerializeField] private Color healthColor = Color.red;
        [SerializeField] private Color lowHealthColor = Color.darkRed;
        [SerializeField] private float lowHealthThreshold = 0.25f;
        
        [Header("Stamina UI")]
        [SerializeField] private Slider staminaSlider;
        [SerializeField] private Image staminaFill;
        [SerializeField] private TextMeshProUGUI staminaText;
        [SerializeField] private Color staminaColor = Color.green;
        [SerializeField] private Color lowStaminaColor = Color.yellow;
        [SerializeField] private float lowStaminaThreshold = 0.3f;
        
        [Header("Animation Settings")] 
        [SerializeField] private float animationSpeed = 2f;
        [SerializeField] private bool smoothTransitions = true;
        
        [Header("Warning Effects")]
        [SerializeField] private GameObject lowHealthWarning;
        [SerializeField] private float warningFlashSpeed = 2f;
        [SerializeField] private AudioClip lowHealthSound;
        [SerializeField] private AudioClip criticalHealthSound;
        
        // Components
        private Player.PlayerHealth playerHealth;
        private Player.PlayerController2D playerController;
        private AudioSource audioSource;
        
        // State tracking
        private float targetHealthValue;
        private float targetStaminaValue;
        private bool isLowHealthWarningActive;
        private float warningTimer;
        
        private void Awake()
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
        }
        
        private void Start()
        {
            // Find player components
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                playerHealth = player.GetComponent<Player.PlayerHealth>();
                playerController = player.GetComponent<Player.PlayerController2D>();
                
                // Subscribe to health events
                if (playerHealth != null)
                {
                    playerHealth.OnHealthChanged.AddListener(UpdateHealthUI);
                    playerHealth.OnPlayerDeath.AddListener(OnPlayerDeath);
                    playerHealth.OnPlayerRespawn.AddListener(OnPlayerRespawn);
                }
            }
            
            // Initialize UI
            InitializeUI();
        }
        
        private void Update()
        {
            UpdateStaminaUI();
            HandleWarningEffects();
            
            if (smoothTransitions)
            {
                AnimateSliders();
            }
        }
        
        private void InitializeUI()
        {
            if (playerHealth != null)
            {
                UpdateHealthUI(playerHealth.CurrentHealth, playerHealth.MaxHealth);
            }
            
            if (playerController != null)
            {
                UpdateStaminaUI();
            }
            
            // Set initial colors
            if (healthFill != null)
                healthFill.color = healthColor;
            
            if (staminaFill != null)
                staminaFill.color = staminaColor;
        }
        
        private void UpdateHealthUI(float currentHealth, float maxHealth)
        {
            float healthPercentage = currentHealth / maxHealth;
            
            if (smoothTransitions)
            {
                targetHealthValue = healthPercentage;
            }
            else
            {
                SetHealthSliderValue(healthPercentage);
            }
            
            // Update health text
            if (healthText != null)
            {
                healthText.text = $"{Mathf.Ceil(currentHealth)}/{Mathf.Ceil(maxHealth)}";
            }
            
            // Update health color based on percentage
            UpdateHealthColor(healthPercentage);
            
            // Handle low health warning
            HandleLowHealthWarning(healthPercentage);
        }
        
        private void UpdateStaminaUI()
        {
            if (playerController == null) return;
            
            float staminaPercentage = playerController.CurrentStamina / playerController.MaxStamina;
            
            if (smoothTransitions)
            {
                targetStaminaValue = staminaPercentage;
            }
            else
            {
                SetStaminaSliderValue(staminaPercentage);
            }
            
            // Update stamina text
            if (staminaText != null)
            {
                staminaText.text = $"{Mathf.Ceil(playerController.CurrentStamina)}/{Mathf.Ceil(playerController.MaxStamina)}";
            }
            
            // Update stamina color based on percentage
            UpdateStaminaColor(staminaPercentage);
        }
        
        private void AnimateSliders()
        {
            // Animate health slider
            if (healthSlider != null)
            {
                float currentValue = healthSlider.value;
                float newValue = Mathf.Lerp(currentValue, targetHealthValue, animationSpeed * Time.deltaTime);
                SetHealthSliderValue(newValue);
            }
            
            // Animate stamina slider
            if (staminaSlider != null)
            {
                float currentValue = staminaSlider.value;
                float newValue = Mathf.Lerp(currentValue, targetStaminaValue, animationSpeed * Time.deltaTime);
                SetStaminaSliderValue(newValue);
            }
        }
        
        private void SetHealthSliderValue(float value)
        {
            if (healthSlider != null)
                healthSlider.value = value;
        }
        
        private void SetStaminaSliderValue(float value)
        {
            if (staminaSlider != null)
                staminaSlider.value = value;
        }
        
        private void UpdateHealthColor(float healthPercentage)
        {
            if (healthFill == null) return;
            
            Color targetColor = healthPercentage <= lowHealthThreshold ? lowHealthColor : healthColor;
            healthFill.color = targetColor;
        }
        
        private void UpdateStaminaColor(float staminaPercentage)
        {
            if (staminaFill == null) return;
            
            Color targetColor = staminaPercentage <= lowStaminaThreshold ? lowStaminaColor : staminaColor;
            staminaFill.color = targetColor;
        }
        
        private void HandleLowHealthWarning(float healthPercentage)
        {
            bool shouldShowWarning = healthPercentage <= lowHealthThreshold;
            
            if (shouldShowWarning && !isLowHealthWarningActive)
            {
                // Start low health warning
                isLowHealthWarningActive = true;
                PlayWarningSound(healthPercentage <= 0.1f);
            }
            else if (!shouldShowWarning && isLowHealthWarningActive)
            {
                // Stop low health warning
                isLowHealthWarningActive = false;
                if (lowHealthWarning != null)
                    lowHealthWarning.SetActive(false);
            }
        }
        
        private void HandleWarningEffects()
        {
            if (!isLowHealthWarningActive || lowHealthWarning == null) return;
            
            warningTimer += Time.deltaTime;
            
            // Flash warning effect
            bool shouldShow = Mathf.Sin(warningTimer * warningFlashSpeed) > 0;
            lowHealthWarning.SetActive(shouldShow);
        }
        
        private void PlayWarningSound(bool critical)
        {
            if (audioSource == null) return;
            
            AudioClip clipToPlay = critical ? criticalHealthSound : lowHealthSound;
            if (clipToPlay != null)
            {
                audioSource.PlayOneShot(clipToPlay);
            }
        }
        
        private void OnPlayerDeath()
        {
            // Handle death UI effects
            isLowHealthWarningActive = false;
            if (lowHealthWarning != null)
                lowHealthWarning.SetActive(false);
            
            // Could add death screen overlay here
        }
        
        private void OnPlayerRespawn()
        {
            // Reset UI state on respawn
            isLowHealthWarningActive = false;
            warningTimer = 0f;
            
            if (lowHealthWarning != null)
                lowHealthWarning.SetActive(false);
        }
        
        // Public methods for external control
        public void SetHealthBarVisibility(bool visible)
        {
            if (healthSlider != null)
                healthSlider.gameObject.SetActive(visible);
        }
        
        public void SetStaminaBarVisibility(bool visible)
        {
            if (staminaSlider != null)
                staminaSlider.gameObject.SetActive(visible);
        }
        
        public void SetHUDVisibility(bool visible)
        {
            gameObject.SetActive(visible);
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (playerHealth != null)
            {
                playerHealth.OnHealthChanged.RemoveListener(UpdateHealthUI);
                playerHealth.OnPlayerDeath.RemoveListener(OnPlayerDeath);
                playerHealth.OnPlayerRespawn.RemoveListener(OnPlayerRespawn);
            }
        }
    }
}
