using UnityEngine;
using UnityEngine.Events;

namespace SoulsLike2D.Player
{
    public class PlayerHealth : MonoBehaviour
    {
        [Header("Health Settings")]
        [SerializeField] private float maxHealth = 100f;
        [SerializeField] private float currentHealth;
        [SerializeField] private float healthRegenRate = 0f; // Souls-like typically don't regen health
        
        [Header("Damage Settings")]
        [SerializeField] private float invulnerabilityDuration = 1f;
        [SerializeField] private float knockbackForce = 5f;
        
        [Header("Death Settings")]
        [SerializeField] private Vector3 respawnPoint;
        [SerializeField] private float respawnDelay = 2f;
        
        // Events
        [System.Serializable]
        public class HealthEvent : UnityEvent<float, float> { } // current, max
        
        [Header("Events")]
        public HealthEvent OnHealthChanged;
        public UnityEvent OnPlayerDeath;
        public UnityEvent OnPlayerRespawn;
        public UnityEvent OnDamageTaken;
        
        // Components
        private PlayerController2D playerController;
        private Rigidbody2D rb;
        private Collider2D playerCollider;
        private SpriteRenderer spriteRenderer;
        
        // State
        private bool isInvulnerable;
        private bool isDead;
        private float invulnerabilityTimer;
        
        private void Awake()
        {
            playerController = GetComponent<PlayerController2D>();
            rb = GetComponent<Rigidbody2D>();
            playerCollider = GetComponent<Collider2D>();
            spriteRenderer = GetComponent<SpriteRenderer>();
            
            currentHealth = maxHealth;
            respawnPoint = transform.position;
        }
        
        private void Update()
        {
            HandleInvulnerability();
            HandleHealthRegen();
        }
        
        private void HandleInvulnerability()
        {
            if (isInvulnerable)
            {
                invulnerabilityTimer -= Time.deltaTime;
                
                // Flashing effect during invulnerability
                if (spriteRenderer != null)
                {
                    float alpha = Mathf.Sin(Time.time * 20f) * 0.5f + 0.5f;
                    Color color = spriteRenderer.color;
                    color.a = Mathf.Lerp(0.3f, 1f, alpha);
                    spriteRenderer.color = color;
                }
                
                if (invulnerabilityTimer <= 0)
                {
                    EndInvulnerability();
                }
            }
        }
        
        private void HandleHealthRegen()
        {
            if (healthRegenRate > 0 && currentHealth < maxHealth && !isDead)
            {
                currentHealth += healthRegenRate * Time.deltaTime;
                currentHealth = Mathf.Clamp(currentHealth, 0, maxHealth);
                OnHealthChanged?.Invoke(currentHealth, maxHealth);
            }
        }
        
        public void TakeDamage(float damage, Vector2 knockbackDirection = default)
        {
            if (isInvulnerable || isDead) return;
            
            // Reduce damage if blocking
            if (playerController != null && playerController.IsBlocking)
            {
                damage *= 0.3f; // Block reduces damage by 70%
                
                // Consume stamina when blocking
                // This would need to be implemented in PlayerController
            }
            
            // Apply damage
            currentHealth -= damage;
            currentHealth = Mathf.Clamp(currentHealth, 0, maxHealth);
            
            // Trigger events
            OnDamageTaken?.Invoke();
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
            
            // Apply knockback
            if (knockbackDirection != Vector2.zero && rb != null)
            {
                rb.linearVelocity = Vector2.zero;
                rb.AddForce(knockbackDirection.normalized * knockbackForce, ForceMode2D.Impulse);
            }
            
            // Start invulnerability
            StartInvulnerability();
            
            // Check for death
            if (currentHealth <= 0)
            {
                Die();
            }
        }
        
        public void Heal(float healAmount)
        {
            if (isDead) return;
            
            currentHealth += healAmount;
            currentHealth = Mathf.Clamp(currentHealth, 0, maxHealth);
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
        }
        
        public void SetMaxHealth(float newMaxHealth)
        {
            float healthPercentage = currentHealth / maxHealth;
            maxHealth = newMaxHealth;
            currentHealth = maxHealth * healthPercentage;
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
        }
        
        private void StartInvulnerability()
        {
            isInvulnerable = true;
            invulnerabilityTimer = invulnerabilityDuration;
        }
        
        private void EndInvulnerability()
        {
            isInvulnerable = false;
            
            if (spriteRenderer != null)
            {
                Color color = spriteRenderer.color;
                color.a = 1f;
                spriteRenderer.color = color;
            }
        }
        
        private void Die()
        {
            if (isDead) return;
            
            isDead = true;
            OnPlayerDeath?.Invoke();
            
            // Disable player controls
            if (playerController != null)
                playerController.enabled = false;
            
            // Play death animation
            // This would trigger death animation
            
            // Respawn after delay
            Invoke(nameof(Respawn), respawnDelay);
        }
        
        private void Respawn()
        {
            isDead = false;
            currentHealth = maxHealth;
            
            // Reset position
            transform.position = respawnPoint;
            
            // Reset velocity
            if (rb != null)
                rb.linearVelocity = Vector2.zero;
            
            // Re-enable player controls
            if (playerController != null)
                playerController.enabled = true;
            
            // End any ongoing invulnerability
            EndInvulnerability();
            
            // Trigger events
            OnPlayerRespawn?.Invoke();
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
        }
        
        public void SetRespawnPoint(Vector3 newRespawnPoint)
        {
            respawnPoint = newRespawnPoint;
        }
        
        // Public getters
        public float CurrentHealth => currentHealth;
        public float MaxHealth => maxHealth;
        public bool IsInvulnerable => isInvulnerable;
        public bool IsDead => isDead;
        public float HealthPercentage => currentHealth / maxHealth;
        
        // For damage dealing (when player attacks enemies)
        private void OnTriggerEnter2D(Collider2D other)
        {
            // This would be handled by a separate combat system
            // Left here as an example of how damage dealing might work
        }
        
        private void OnDrawGizmosSelected()
        {
            // Draw respawn point
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(respawnPoint, 0.5f);
            Gizmos.DrawLine(transform.position, respawnPoint);
        }
    }
}
