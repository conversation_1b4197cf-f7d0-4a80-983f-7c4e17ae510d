using UnityEngine;

namespace SoulsLike2D.TestScene
{
    public class DamageTestTrigger : MonoBehaviour
    {
        [Header("Damage Settings")]
        [SerializeField] private float damageAmount = 20f;
        [SerializeField] private float knockbackForce = 5f;
        [SerializeField] private float cooldownTime = 1f;
        
        [Header("Visual Settings")]
        [SerializeField] private Color normalColor = Color.red;
        [SerializeField] private Color cooldownColor = Color.gray;
        [SerializeField] private bool flashOnTrigger = true;
        [SerializeField] private float flashDuration = 0.2f;
        
        [Header("Audio")]
        [SerializeField] private AudioClip damageSound;
        
        // Components
        private Renderer objectRenderer;
        private AudioSource audioSource;
        private Collider objectCollider;
        
        // State
        private bool isOnCooldown = false;
        private float cooldownTimer = 0f;
        private Color originalColor;
        
        private void Awake()
        {
            objectRenderer = GetComponent<Renderer>();
            audioSource = GetComponent<AudioSource>();
            objectCollider = GetComponent<Collider>();
            
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();
            
            if (objectRenderer != null)
                originalColor = objectRenderer.material.color;
        }
        
        private void Start()
        {
            // Set initial color
            if (objectRenderer != null)
                objectRenderer.material.color = normalColor;
        }
        
        private void Update()
        {
            HandleCooldown();
        }
        
        private void HandleCooldown()
        {
            if (isOnCooldown)
            {
                cooldownTimer -= Time.deltaTime;
                
                if (cooldownTimer <= 0f)
                {
                    isOnCooldown = false;
                    
                    // Reset visual
                    if (objectRenderer != null)
                        objectRenderer.material.color = normalColor;
                }
            }
        }
        
        private void OnTriggerEnter2D(Collider2D other)
        {
            HandleTrigger(other.gameObject);
        }
        
        private void OnTriggerEnter(Collider other)
        {
            HandleTrigger(other.gameObject);
        }
        
        private void HandleTrigger(GameObject other)
        {
            if (isOnCooldown) return;
            
            // Check if it's the player
            if (other.CompareTag("Player"))
            {
                Player.PlayerHealth playerHealth = other.GetComponent<Player.PlayerHealth>();
                if (playerHealth != null)
                {
                    // Calculate knockback direction
                    Vector2 knockbackDirection = (other.transform.position - transform.position).normalized;
                    
                    // Deal damage
                    playerHealth.TakeDamage(damageAmount, knockbackDirection * knockbackForce);
                    
                    // Start cooldown
                    StartCooldown();
                    
                    // Play effects
                    PlayEffects();
                    
                    Debug.Log($"Player took {damageAmount} damage from {gameObject.name}");
                }
            }
        }
        
        private void StartCooldown()
        {
            isOnCooldown = true;
            cooldownTimer = cooldownTime;
            
            // Change visual to indicate cooldown
            if (objectRenderer != null)
                objectRenderer.material.color = cooldownColor;
        }
        
        private void PlayEffects()
        {
            // Play sound
            if (audioSource != null && damageSound != null)
            {
                audioSource.PlayOneShot(damageSound);
            }
            
            // Flash effect
            if (flashOnTrigger)
            {
                StartCoroutine(FlashEffect());
            }
        }
        
        private System.Collections.IEnumerator FlashEffect()
        {
            if (objectRenderer == null) yield break;
            
            Color startColor = objectRenderer.material.color;
            Color flashColor = Color.white;
            
            float elapsed = 0f;
            while (elapsed < flashDuration)
            {
                float t = elapsed / flashDuration;
                Color currentColor = Color.Lerp(flashColor, startColor, t);
                objectRenderer.material.color = currentColor;
                
                elapsed += Time.deltaTime;
                yield return null;
            }
            
            objectRenderer.material.color = isOnCooldown ? cooldownColor : normalColor;
        }
        
        // Public methods for external control
        public void SetDamageAmount(float newDamage)
        {
            damageAmount = newDamage;
        }
        
        public void SetKnockbackForce(float newForce)
        {
            knockbackForce = newForce;
        }
        
        public void TriggerDamage()
        {
            // Manual trigger for testing
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                HandleTrigger(player);
            }
        }
        
        public void ResetCooldown()
        {
            isOnCooldown = false;
            cooldownTimer = 0f;
            
            if (objectRenderer != null)
                objectRenderer.material.color = normalColor;
        }
        
        // Debug info
        private void OnDrawGizmosSelected()
        {
            // Draw damage range
            Gizmos.color = isOnCooldown ? Color.gray : Color.red;
            
            if (objectCollider != null)
            {
                if (objectCollider is SphereCollider sphere)
                {
                    Gizmos.DrawWireSphere(transform.position, sphere.radius);
                }
                else if (objectCollider is BoxCollider box)
                {
                    Gizmos.matrix = Matrix4x4.TRS(transform.position, transform.rotation, transform.lossyScale);
                    Gizmos.DrawWireCube(Vector3.zero, box.size);
                    Gizmos.matrix = Matrix4x4.identity;
                }
            }
            
            // Draw knockback direction indicator
            Gizmos.color = Color.yellow;
            GameObject player = GameObject.FindGameObjectWithTag("Player");
            if (player != null)
            {
                Vector3 direction = (player.transform.position - transform.position).normalized;
                Gizmos.DrawRay(transform.position, direction * knockbackForce);
            }
        }
    }
}
