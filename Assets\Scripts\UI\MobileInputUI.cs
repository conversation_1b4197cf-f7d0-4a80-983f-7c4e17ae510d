using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

namespace SoulsLike2D.UI
{
    public class MobileInputUI : MonoBehaviour
    {
        [Header("Virtual Joystick")]
        [SerializeField] private RectTransform joystickBackground;
        [SerializeField] private RectTransform joystickHandle;
        [SerializeField] private float joystickRange = 50f;
        [SerializeField] private bool dynamicJoystick = true;
        
        [Header("Action Buttons")]
        [SerializeField] private Button jumpButton;
        [SerializeField] private Button attackButton;
        [SerializeField] private Button heavyAttackButton;
        [SerializeField] private Button dodgeButton;
        [SerializeField] private But<PERSON> blockButton;
        
        [Header("UI Settings")]
        [SerializeField] private float buttonFeedbackScale = 0.9f;
        [SerializeField] private float feedbackDuration = 0.1f;
        [SerializeField] private CanvasGroup mobileUIGroup;
        
        // Joystick state
        private Vector2 joystickInput;
        private bool isDragging;
        private Vector2 joystickCenter;
        private int joystickPointerId = -1;
        
        // Button states
        private bool isBlockPressed;
        
        // Components
        private Canvas canvas;
        
        private void Awake()
        {
            canvas = GetComponentInParent<Canvas>();
            
            // Set up joystick center
            if (joystickBackground != null)
            {
                joystickCenter = joystickBackground.anchoredPosition;
            }
            
            // Hide mobile UI on non-mobile platforms in editor
            #if UNITY_EDITOR
            if (mobileUIGroup != null)
            {
                mobileUIGroup.alpha = 1f; // Show in editor for testing
            }
            #elif UNITY_STANDALONE || UNITY_WEBGL
            if (mobileUIGroup != null)
            {
                mobileUIGroup.alpha = 0f;
                mobileUIGroup.interactable = false;
            }
            #endif
        }
        
        private void Start()
        {
            SetupButtons();
        }
        
        private void Update()
        {
            HandleTouchInput();
        }
        
        private void SetupButtons()
        {
            // Setup button events
            if (jumpButton != null)
            {
                jumpButton.onClick.AddListener(OnJumpPressed);
                AddButtonFeedback(jumpButton);
            }
            
            if (attackButton != null)
            {
                attackButton.onClick.AddListener(OnAttackPressed);
                AddButtonFeedback(attackButton);
            }
            
            if (heavyAttackButton != null)
            {
                // Heavy attack uses pointer events for hold detection
                EventTrigger trigger = heavyAttackButton.gameObject.GetComponent<EventTrigger>();
                if (trigger == null)
                    trigger = heavyAttackButton.gameObject.AddComponent<EventTrigger>();
                
                EventTrigger.Entry pointerDown = new EventTrigger.Entry();
                pointerDown.eventID = EventTriggerType.PointerDown;
                pointerDown.callback.AddListener((data) => { OnHeavyAttackStart(); });
                trigger.triggers.Add(pointerDown);
                
                EventTrigger.Entry pointerUp = new EventTrigger.Entry();
                pointerUp.eventID = EventTriggerType.PointerUp;
                pointerUp.callback.AddListener((data) => { OnHeavyAttackEnd(); });
                trigger.triggers.Add(pointerUp);
                
                AddButtonFeedback(heavyAttackButton);
            }
            
            if (dodgeButton != null)
            {
                dodgeButton.onClick.AddListener(OnDodgePressed);
                AddButtonFeedback(dodgeButton);
            }
            
            if (blockButton != null)
            {
                // Block uses pointer events for hold detection
                EventTrigger trigger = blockButton.gameObject.GetComponent<EventTrigger>();
                if (trigger == null)
                    trigger = blockButton.gameObject.AddComponent<EventTrigger>();
                
                EventTrigger.Entry pointerDown = new EventTrigger.Entry();
                pointerDown.eventID = EventTriggerType.PointerDown;
                pointerDown.callback.AddListener((data) => { OnBlockStart(); });
                trigger.triggers.Add(pointerDown);
                
                EventTrigger.Entry pointerUp = new EventTrigger.Entry();
                pointerUp.eventID = EventTriggerType.PointerUp;
                pointerUp.callback.AddListener((data) => { OnBlockEnd(); });
                trigger.triggers.Add(pointerUp);
                
                AddButtonFeedback(blockButton);
            }
        }
        
        private void AddButtonFeedback(Button button)
        {
            // Add visual feedback to buttons
            EventTrigger trigger = button.gameObject.GetComponent<EventTrigger>();
            if (trigger == null)
                trigger = button.gameObject.AddComponent<EventTrigger>();
            
            EventTrigger.Entry pointerDown = new EventTrigger.Entry();
            pointerDown.eventID = EventTriggerType.PointerDown;
            pointerDown.callback.AddListener((data) => { 
                StartCoroutine(ButtonFeedback(button.transform, true)); 
            });
            trigger.triggers.Add(pointerDown);
            
            EventTrigger.Entry pointerUp = new EventTrigger.Entry();
            pointerUp.eventID = EventTriggerType.PointerUp;
            pointerUp.callback.AddListener((data) => { 
                StartCoroutine(ButtonFeedback(button.transform, false)); 
            });
            trigger.triggers.Add(pointerUp);
        }
        
        private System.Collections.IEnumerator ButtonFeedback(Transform buttonTransform, bool pressed)
        {
            Vector3 targetScale = pressed ? Vector3.one * buttonFeedbackScale : Vector3.one;
            Vector3 startScale = buttonTransform.localScale;
            
            float elapsed = 0f;
            while (elapsed < feedbackDuration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / feedbackDuration;
                buttonTransform.localScale = Vector3.Lerp(startScale, targetScale, t);
                yield return null;
            }
            
            buttonTransform.localScale = targetScale;
        }
        
        private void HandleTouchInput()
        {
            // Handle joystick input
            for (int i = 0; i < Input.touchCount; i++)
            {
                Touch touch = Input.GetTouch(i);
                Vector2 touchPosition = touch.position;
                
                // Convert screen position to canvas position
                RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    canvas.transform as RectTransform, 
                    touchPosition, 
                    canvas.worldCamera, 
                    out Vector2 localPoint);
                
                HandleJoystickInput(touch, localPoint);
            }
            
            // Handle mouse input for testing in editor
            #if UNITY_EDITOR
            HandleMouseInput();
            #endif
        }
        
        private void HandleJoystickInput(Touch touch, Vector2 localPoint)
        {
            if (joystickBackground == null || joystickHandle == null) return;
            
            switch (touch.phase)
            {
                case TouchPhase.Began:
                    if (RectTransformUtility.RectangleContainsScreenPoint(joystickBackground, touch.position, canvas.worldCamera))
                    {
                        joystickPointerId = touch.fingerId;
                        isDragging = true;
                        
                        if (dynamicJoystick)
                        {
                            joystickCenter = localPoint;
                            joystickBackground.anchoredPosition = joystickCenter;
                        }
                    }
                    break;
                    
                case TouchPhase.Moved:
                    if (touch.fingerId == joystickPointerId && isDragging)
                    {
                        UpdateJoystick(localPoint);
                    }
                    break;
                    
                case TouchPhase.Ended:
                case TouchPhase.Canceled:
                    if (touch.fingerId == joystickPointerId)
                    {
                        ResetJoystick();
                    }
                    break;
            }
        }
        
        #if UNITY_EDITOR
        private void HandleMouseInput()
        {
            Vector2 mousePosition = Input.mousePosition;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                canvas.transform as RectTransform, 
                mousePosition, 
                canvas.worldCamera, 
                out Vector2 localPoint);
            
            if (Input.GetMouseButtonDown(0))
            {
                if (RectTransformUtility.RectangleContainsScreenPoint(joystickBackground, mousePosition, canvas.worldCamera))
                {
                    isDragging = true;
                    if (dynamicJoystick)
                    {
                        joystickCenter = localPoint;
                        joystickBackground.anchoredPosition = joystickCenter;
                    }
                }
            }
            else if (Input.GetMouseButton(0) && isDragging)
            {
                UpdateJoystick(localPoint);
            }
            else if (Input.GetMouseButtonUp(0))
            {
                ResetJoystick();
            }
        }
        #endif
        
        private void UpdateJoystick(Vector2 inputPosition)
        {
            Vector2 direction = inputPosition - joystickCenter;
            float distance = direction.magnitude;
            
            if (distance > joystickRange)
            {
                direction = direction.normalized * joystickRange;
            }
            
            joystickHandle.anchoredPosition = joystickCenter + direction;
            joystickInput = direction / joystickRange;
        }
        
        private void ResetJoystick()
        {
            isDragging = false;
            joystickPointerId = -1;
            joystickInput = Vector2.zero;
            joystickHandle.anchoredPosition = joystickCenter;
        }
        
        // Button event handlers
        private void OnJumpPressed()
        {
            // This would trigger the jump input action
            Debug.Log("Jump pressed");
        }
        
        private void OnAttackPressed()
        {
            // This would trigger the attack input action
            Debug.Log("Attack pressed");
        }
        
        private void OnHeavyAttackStart()
        {
            // This would start the heavy attack input action
            Debug.Log("Heavy attack started");
        }
        
        private void OnHeavyAttackEnd()
        {
            // This would end the heavy attack input action
            Debug.Log("Heavy attack ended");
        }
        
        private void OnDodgePressed()
        {
            // This would trigger the dodge input action
            Debug.Log("Dodge pressed");
        }
        
        private void OnBlockStart()
        {
            isBlockPressed = true;
            Debug.Log("Block started");
        }
        
        private void OnBlockEnd()
        {
            isBlockPressed = false;
            Debug.Log("Block ended");
        }
        
        // Public getters for input values
        public Vector2 JoystickInput => joystickInput;
        public bool IsBlockPressed => isBlockPressed;
        
        public void SetUIVisibility(bool visible)
        {
            if (mobileUIGroup != null)
            {
                mobileUIGroup.alpha = visible ? 1f : 0f;
                mobileUIGroup.interactable = visible;
            }
        }
    }
}
