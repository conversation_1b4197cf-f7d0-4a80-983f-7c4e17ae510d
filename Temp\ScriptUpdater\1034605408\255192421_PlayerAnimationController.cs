using UnityEngine;

namespace SoulsLike2D.Player
{
    [RequireComponent(typeof(Animator))]
    public class PlayerAnimationController : MonoBehaviour
    {
        [Header("Animation Settings")]
        [SerializeField] private float animationTransitionSpeed = 0.1f;
        
        // Components
        private Animator animator;
        private PlayerController2D playerController;
        private PlayerHealth playerHealth;
        private Rigidbody2D rb;
        
        // Animation parameter hashes (for performance)
        private int speedHash;
        private int isGroundedHash;
        private int isDodgingHash;
        private int isBlockingHash;
        private int velocityYHash;
        private int attackHash;
        private int heavyAttackHash;
        private int dodgeHash;
        private int hurtHash;
        private int deathHash;
        private int isDeadHash;
        
        private void Awake()
        {
            animator = GetComponent<Animator>();
            playerController = GetComponent<PlayerController2D>();
            playerHealth = GetComponent<PlayerHealth>();
            rb = GetComponent<Rigidbody2D>();
            
            // Cache animation parameter hashes
            speedHash = Animator.StringToHash("Speed");
            isGroundedHash = Animator.StringToHash("IsGrounded");
            isDodgingHash = Animator.StringToHash("IsDodging");
            isBlockingHash = Animator.StringToHash("IsBlocking");
            velocityYHash = Animator.StringToHash("VelocityY");
            attackHash = Animator.StringToHash("Attack");
            heavyAttackHash = Animator.StringToHash("HeavyAttack");
            dodgeHash = Animator.StringToHash("Dodge");
            hurtHash = Animator.StringToHash("Hurt");
            deathHash = Animator.StringToHash("Death");
            isDeadHash = Animator.StringToHash("IsDead");
        }
        
        private void OnEnable()
        {
            // Subscribe to health events
            if (playerHealth != null)
            {
                playerHealth.OnDamageTaken.AddListener(PlayHurtAnimation);
                playerHealth.OnPlayerDeath.AddListener(PlayDeathAnimation);
                playerHealth.OnPlayerRespawn.AddListener(PlayRespawnAnimation);
            }
        }
        
        private void OnDisable()
        {
            // Unsubscribe from health events
            if (playerHealth != null)
            {
                playerHealth.OnDamageTaken.RemoveListener(PlayHurtAnimation);
                playerHealth.OnPlayerDeath.RemoveListener(PlayDeathAnimation);
                playerHealth.OnPlayerRespawn.RemoveListener(PlayRespawnAnimation);
            }
        }
        
        private void Update()
        {
            UpdateAnimationParameters();
        }
        
        private void UpdateAnimationParameters()
        {
            if (animator == null) return;
            
            // Movement animations
            if (playerController != null)
            {
                // Get horizontal movement speed
                float horizontalSpeed = Mathf.Abs(rb.linearVelocity.x);
                animator.SetFloat(speedHash, horizontalSpeed, animationTransitionSpeed, Time.deltaTime);
                
                // Ground state
                animator.SetBool(isGroundedHash, IsGrounded());
                
                // Vertical velocity for jump/fall animations
                animator.SetFloat(velocityYHash, rb.linearVelocity.y);
                
                // Combat states
                animator.SetBool(isDodgingHash, playerController.IsDodging);
                animator.SetBool(isBlockingHash, playerController.IsBlocking);
            }
            
            // Health state
            if (playerHealth != null)
            {
                animator.SetBool(isDeadHash, playerHealth.IsDead);
            }
        }
        
        private bool IsGrounded()
        {
            // This should match the ground detection logic in PlayerController2D
            // For now, we'll use a simple raycast
            RaycastHit2D hit = Physics2D.Raycast(transform.position, Vector2.down, 1.1f);
            return hit.collider != null;
        }
        
        public void PlayAttackAnimation()
        {
            if (animator != null)
            {
                animator.SetTrigger(attackHash);
            }
        }
        
        public void PlayHeavyAttackAnimation()
        {
            if (animator != null)
            {
                animator.SetTrigger(heavyAttackHash);
            }
        }
        
        public void PlayDodgeAnimation()
        {
            if (animator != null)
            {
                animator.SetTrigger(dodgeHash);
            }
        }
        
        public void PlayHurtAnimation()
        {
            if (animator != null && !playerHealth.IsDead)
            {
                animator.SetTrigger(hurtHash);
            }
        }
        
        public void PlayDeathAnimation()
        {
            if (animator != null)
            {
                animator.SetTrigger(deathHash);
                animator.SetBool(isDeadHash, true);
            }
        }
        
        public void PlayRespawnAnimation()
        {
            if (animator != null)
            {
                animator.SetBool(isDeadHash, false);
                // Reset any other animation states
                ResetAnimationStates();
            }
        }
        
        private void ResetAnimationStates()
        {
            if (animator == null) return;
            
            // Reset all boolean parameters
            animator.SetBool(isDodgingHash, false);
            animator.SetBool(isBlockingHash, false);
            animator.SetBool(isDeadHash, false);
            
            // Reset float parameters
            animator.SetFloat(speedHash, 0f);
            animator.SetFloat(velocityYHash, 0f);
        }
        
        // Animation Events (called from animation clips)
        public void OnAttackStart()
        {
            // Called at the beginning of attack animation
            // Can be used to enable hitboxes, play sound effects, etc.
        }
        
        public void OnAttackHit()
        {
            // Called at the moment of impact in attack animation
            // This is where damage would be dealt
        }
        
        public void OnAttackEnd()
        {
            // Called at the end of attack animation
            // Can be used to disable hitboxes, allow movement, etc.
            if (playerController != null)
            {
                // This would call a method to end the attack state
                // playerController.EndAttack();
            }
        }
        
        public void OnDodgeStart()
        {
            // Called at the beginning of dodge animation
            // Can be used to enable invulnerability frames
        }
        
        public void OnDodgeEnd()
        {
            // Called at the end of dodge animation
            // Can be used to disable invulnerability frames
        }
        
        public void OnDeathComplete()
        {
            // Called when death animation is complete
            // Can be used to trigger respawn or game over screen
        }
        
        // Utility methods for other systems
        public bool IsPlayingAnimation(string animationName)
        {
            if (animator == null) return false;
            
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(0);
            return stateInfo.IsName(animationName);
        }
        
        public float GetCurrentAnimationLength()
        {
            if (animator == null) return 0f;
            
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(0);
            return stateInfo.length;
        }
        
        public float GetCurrentAnimationNormalizedTime()
        {
            if (animator == null) return 0f;
            
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(0);
            return stateInfo.normalizedTime;
        }
        
        // For debugging
        private void OnDrawGizmosSelected()
        {
            // Draw ground check ray
            Gizmos.color = Color.red;
            Gizmos.DrawRay(transform.position, Vector2.down * 1.1f);
        }
    }
}
