# 2D Souls-like Mobile Game - Player Controller System

## Обзор системы

Эта система управления персонажем создана специально для 2D Souls-like игры на мобильных устройствах (iOS/Android). Система включает в себя:

### 🎮 Основные компоненты:

1. **PlayerController2D** - Основной контроллер персонажа
2. **PlayerHealth** - Система здоровья и смерти
3. **PlayerAnimationController** - Управление анимациями
4. **MobileInputUI** - Мобильный интерфейс управления
5. **PlayerHUD** - Интерфейс здоровья и выносливости
6. **GameManager** - Основной менеджер игры

### 🚀 Особенности:

- ✅ Полная поддержка сенсорного управления
- ✅ Система выносливости (Stamina) как в Souls-играх
- ✅ Система уклонений с i-frames
- ✅ Блокирование атак
- ✅ Легкие и тяжелые атаки
- ✅ Адаптивный UI для разных размеров экранов
- ✅ Система здоровья с возрождением
- ✅ Оптимизация для мобильных устройств

## 📱 Настройка для мобильных устройств

### 1. Настройка Input System

Файл `InputSystem_Actions.inputactions` уже настроен с поддержкой:
- Сенсорного управления
- Геймпада
- Клавиатуры (для тестирования)

### 2. Создание персонажа

1. Создайте пустой GameObject с именем "Player"
2. Добавьте компоненты:
   - `Rigidbody2D`
   - `Collider2D` (например, CapsuleCollider2D)
   - `SpriteRenderer`
   - `Animator`
   - `PlayerInput`
   - `PlayerController2D`
   - `PlayerHealth`
   - `PlayerAnimationController`

3. Настройте Rigidbody2D:
   - Freeze Rotation Z = true
   - Gravity Scale = 3-5

### 3. Настройка Ground Check

1. Создайте дочерний пустой GameObject с именем "GroundCheck"
2. Расположите его у ног персонажа
3. Назначьте его в поле Ground Check в PlayerController2D

### 4. Создание UI

#### Мобильный UI:
1. Создайте Canvas с Canvas Scaler (Scale With Screen Size)
2. Добавьте виртуальный джойстик:
   - Background (Image)
   - Handle (Image, дочерний к Background)
3. Добавьте кнопки действий:
   - Jump Button
   - Attack Button
   - Heavy Attack Button
   - Dodge Button
   - Block Button
4. Добавьте компонент `MobileInputUI`

#### HUD:
1. Создайте слайдеры для здоровья и выносливости
2. Добавьте компонент `PlayerHUD`

### 5. Настройка анимаций

Создайте Animator Controller с параметрами:
- `Speed` (Float) - скорость движения
- `IsGrounded` (Bool) - на земле ли персонаж
- `VelocityY` (Float) - вертикальная скорость
- `IsDodging` (Bool) - уклонение
- `IsBlocking` (Bool) - блокирование
- `Attack` (Trigger) - легкая атака
- `HeavyAttack` (Trigger) - тяжелая атака
- `Dodge` (Trigger) - уклонение
- `Hurt` (Trigger) - получение урона
- `Death` (Trigger) - смерть
- `IsDead` (Bool) - мертв ли персонаж

## 🎯 Управление

### Мобильное управление:
- **Виртуальный джойстик** - движение
- **Кнопка прыжка** - прыжок
- **Кнопка атаки** - легкая атака
- **Удержание кнопки тяжелой атаки** - тяжелая атака
- **Кнопка уклонения** - уклонение (тратит выносливость)
- **Удержание кнопки блока** - блокирование

### Клавиатура (для тестирования):
- **WASD/Стрелки** - движение
- **Пробел** - прыжок
- **ЛКМ** - легкая атака
- **Удержание ЛКМ** - тяжелая атака
- **Shift** - уклонение
- **ПКМ** - блокирование

## ⚙️ Настройки производительности

### Для мобильных устройств:
1. **Build Settings**:
   - Platform: Android/iOS
   - Scripting Backend: IL2CPP
   - Target Architecture: ARM64

2. **Player Settings**:
   - Color Space: Gamma (для лучшей производительности)
   - Graphics API: OpenGLES3/Metal
   - Multithreaded Rendering: включено

3. **Quality Settings**:
   - Создайте профили для разных устройств
   - Отключите ненужные эффекты
   - Ограничьте разрешение текстур

## 🔧 Кастомизация

### Изменение параметров персонажа:
```csharp
[Header("Movement Settings")]
[SerializeField] private float moveSpeed = 5f;        // Скорость движения
[SerializeField] private float jumpForce = 12f;       // Сила прыжка
[SerializeField] private float dodgeForce = 8f;       // Сила уклонения
[SerializeField] private float dodgeDuration = 0.3f;  // Длительность уклонения

[Header("Stamina System")]
[SerializeField] private float maxStamina = 100f;           // Максимальная выносливость
[SerializeField] private float staminaRegenRate = 20f;      // Скорость восстановления
[SerializeField] private float dodgeStaminaCost = 25f;      // Стоимость уклонения
[SerializeField] private float attackStaminaCost = 15f;     // Стоимость атаки
[SerializeField] private float heavyAttackStaminaCost = 35f; // Стоимость тяжелой атаки
```

### Изменение параметров здоровья:
```csharp
[Header("Health Settings")]
[SerializeField] private float maxHealth = 100f;              // Максимальное здоровье
[SerializeField] private float invulnerabilityDuration = 1f;  // Время неуязвимости
[SerializeField] private float knockbackForce = 5f;           // Сила отбрасывания
```

## 🐛 Отладка

Нажмите F1 в игре для отображения отладочной информации:
- FPS
- Time Scale
- Разрешение экрана
- Платформа
- Состояние мобильного UI

## 📋 TODO / Следующие шаги

1. ✅ Базовая система управления
2. ✅ Мобильный UI
3. ✅ Система здоровья и выносливости
4. ⏳ Система боя с врагами
5. ⏳ Система прогрессии
6. ⏳ Система сохранений
7. ⏳ Звуковые эффекты
8. ⏳ Визуальные эффекты

## 🤝 Поддержка

Если у вас возникли вопросы или проблемы:
1. Проверьте настройки Input System
2. Убедитесь, что все компоненты добавлены к персонажу
3. Проверьте настройки слоев для Ground Check
4. Убедитесь, что Canvas настроен правильно для мобильных устройств
