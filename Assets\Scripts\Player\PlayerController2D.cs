using UnityEngine;
using UnityEngine.InputSystem;

namespace SoulsLike2D.Player
{
    [RequireComponent(typeof(Rigidbody2D))]
    [RequireComponent(typeof(Collider2D))]
    [RequireComponent(typeof(PlayerInput))]
    public class PlayerController2D : MonoBehaviour
    {
        [Header("Movement Settings")]
        [SerializeField] private float moveSpeed = 5f;
        [SerializeField] private float jumpForce = 12f;
        [SerializeField] private float dodgeForce = 8f;
        [SerializeField] private float dodgeDuration = 0.3f;
        
        [Header("Ground Detection")]
        [SerializeField] private Transform groundCheck;
        [SerializeField] private LayerMask groundLayerMask = 1;
        [SerializeField] private float groundCheckRadius = 0.2f;
        
        [Header("Stamina System")]
        [SerializeField] private float maxStamina = 100f;
        [SerializeField] private float staminaRegenRate = 20f;
        [SerializeField] private float dodgeStaminaCost = 25f;
        [SerializeField] private float attackStaminaCost = 15f;
        [SerializeField] private float heavyAttackStaminaCost = 35f;
        
        // Components
        private Rigidbody2D rb;
        private PlayerInput playerInput;
        private Animator animator;
        
        // Input Actions
        private InputAction moveAction;
        private InputAction jumpAction;
        private InputAction attackAction;
        private InputAction heavyAttackAction;
        private InputAction dodgeAction;
        private InputAction blockAction;
        
        // State Variables
        private Vector2 moveInput;
        private bool isGrounded;
        private bool isDodging;
        private bool isBlocking;
        private bool isAttacking;
        private float currentStamina;
        private float dodgeTimer;
        
        // Facing Direction
        private bool facingRight = true;
        
        private void Awake()
        {
            rb = GetComponent<Rigidbody2D>();
            playerInput = GetComponent<PlayerInput>();
            animator = GetComponent<Animator>();
            
            currentStamina = maxStamina;
            
            // Get input actions
            moveAction = playerInput.actions["Move"];
            jumpAction = playerInput.actions["Jump"];
            attackAction = playerInput.actions["Attack"];
            heavyAttackAction = playerInput.actions["HeavyAttack"];
            dodgeAction = playerInput.actions["Dodge"];
            blockAction = playerInput.actions["Block"];
        }
        
        private void OnEnable()
        {
            // Subscribe to input events
            jumpAction.performed += OnJump;
            attackAction.performed += OnAttack;
            heavyAttackAction.performed += OnHeavyAttack;
            dodgeAction.performed += OnDodge;
        }
        
        private void OnDisable()
        {
            // Unsubscribe from input events
            jumpAction.performed -= OnJump;
            attackAction.performed -= OnAttack;
            heavyAttackAction.performed -= OnHeavyAttack;
            dodgeAction.performed -= OnDodge;
        }
        
        private void Update()
        {
            HandleInput();
            CheckGrounded();
            HandleStamina();
            HandleDodge();
            UpdateAnimations();
        }
        
        private void FixedUpdate()
        {
            HandleMovement();
        }
        
        private void HandleInput()
        {
            moveInput = moveAction.ReadValue<Vector2>();
            isBlocking = blockAction.IsPressed();
        }
        
        private void HandleMovement()
        {
            if (isDodging || isAttacking) return;
            
            // Horizontal movement
            float targetVelocityX = moveInput.x * moveSpeed;
            rb.linearVelocity = new Vector2(targetVelocityX, rb.linearVelocity.y);
            
            // Handle facing direction
            if (moveInput.x > 0 && !facingRight)
                Flip();
            else if (moveInput.x < 0 && facingRight)
                Flip();
        }
        
        private void CheckGrounded()
        {
            isGrounded = Physics2D.OverlapCircle(groundCheck.position, groundCheckRadius, groundLayerMask);
        }
        
        private void HandleStamina()
        {
            if (currentStamina < maxStamina && !isDodging && !isAttacking)
            {
                currentStamina += staminaRegenRate * Time.deltaTime;
                currentStamina = Mathf.Clamp(currentStamina, 0, maxStamina);
            }
        }
        
        private void HandleDodge()
        {
            if (isDodging)
            {
                dodgeTimer -= Time.deltaTime;
                if (dodgeTimer <= 0)
                {
                    isDodging = false;
                }
            }
        }
        
        private void OnJump(InputAction.CallbackContext context)
        {
            if (isGrounded && !isDodging && !isAttacking)
            {
                rb.linearVelocity = new Vector2(rb.linearVelocity.x, jumpForce);
            }
        }
        
        private void OnAttack(InputAction.CallbackContext context)
        {
            if (CanPerformAction(attackStaminaCost))
            {
                PerformAttack(false);
            }
        }
        
        private void OnHeavyAttack(InputAction.CallbackContext context)
        {
            if (CanPerformAction(heavyAttackStaminaCost))
            {
                PerformAttack(true);
            }
        }
        
        private void OnDodge(InputAction.CallbackContext context)
        {
            if (CanPerformAction(dodgeStaminaCost) && isGrounded)
            {
                PerformDodge();
            }
        }
        
        private bool CanPerformAction(float staminaCost)
        {
            return currentStamina >= staminaCost && !isDodging && !isAttacking && !isBlocking;
        }
        
        private void PerformAttack(bool isHeavy)
        {
            isAttacking = true;
            float staminaCost = isHeavy ? heavyAttackStaminaCost : attackStaminaCost;
            currentStamina -= staminaCost;
            
            // Animation will handle the attack duration
            if (animator != null)
            {
                animator.SetTrigger(isHeavy ? "HeavyAttack" : "Attack");
            }
            
            // This would be called by animation event or after a delay
            Invoke(nameof(EndAttack), isHeavy ? 1.2f : 0.6f);
        }
        
        private void PerformDodge()
        {
            isDodging = true;
            dodgeTimer = dodgeDuration;
            currentStamina -= dodgeStaminaCost;
            
            // Apply dodge force
            Vector2 dodgeDirection = facingRight ? Vector2.right : Vector2.left;
            if (moveInput.x != 0)
            {
                dodgeDirection = new Vector2(moveInput.x, 0).normalized;
            }
            
            rb.linearVelocity = new Vector2(dodgeDirection.x * dodgeForce, rb.linearVelocity.y);
            
            if (animator != null)
            {
                animator.SetTrigger("Dodge");
            }
        }
        
        private void EndAttack()
        {
            isAttacking = false;
        }
        
        private void Flip()
        {
            facingRight = !facingRight;
            Vector3 scale = transform.localScale;
            scale.x *= -1;
            transform.localScale = scale;
        }
        
        private void UpdateAnimations()
        {
            if (animator == null) return;
            
            animator.SetFloat("Speed", Mathf.Abs(moveInput.x));
            animator.SetBool("IsGrounded", isGrounded);
            animator.SetBool("IsDodging", isDodging);
            animator.SetBool("IsBlocking", isBlocking);
            animator.SetFloat("VelocityY", rb.linearVelocity.y);
        }
        
        // Public getters for other systems
        public float CurrentStamina => currentStamina;
        public float MaxStamina => maxStamina;
        public bool IsBlocking => isBlocking;
        public bool IsDodging => isDodging;
        public bool IsAttacking => isAttacking;
        
        private void OnDrawGizmosSelected()
        {
            if (groundCheck != null)
            {
                Gizmos.color = isGrounded ? Color.green : Color.red;
                Gizmos.DrawWireSphere(groundCheck.position, groundCheckRadius);
            }
        }
    }
}
