﻿<?xml version="1.0" encoding="utf-8"?>
<UXML
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="UnityEngine.UIElements"
    xsi:noNamespaceSchemaLocation="../UIElementsSchema/UIElements.xsd"
    xsi:schemaLocation="UnityEngine.UIElements ../UIElementsSchema/UnityEngine.UIElements.xsd">
    <VisualElement name="loginContainer" class="flex-container main">
        <Label name="signInLabel" class="title"/>
        <TextField name="email"/>
        <Label name="emailNotification" class="alert-label"/>
        <TextField name="password" password="true"/>
        <Label name="passwordNotification" class="alert-label"/>

        <VisualElement class="row align-end">
            <Button name="back" class="classic-button"/>
            <Button name="signIn" class="classic-button"/>
        </VisualElement>

        <VisualElement name="progressContainer"/>

        <VisualElement name="signUpNeededNotificationContainer" class="collapse row">
            <Label name="signUpNeededNotificationLabel"/>
            <Button name="signUpButton" class="anchor"/>
        </VisualElement>
    </VisualElement>
</UXML>