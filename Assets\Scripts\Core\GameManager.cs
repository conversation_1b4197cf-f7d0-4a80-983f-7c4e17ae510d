using UnityEngine;
using UnityEngine.SceneManagement;

namespace SoulsLike2D.Core
{
    public class GameManager : MonoBehaviour
    {
        [Head<PERSON>("Game Settings")]
        [SerializeField] private bool pauseOnFocusLoss = true;
        [SerializeField] private float timeScale = 1f;
        
        [Header("Mobile Settings")]
        [SerializeField] private bool forceMobileUI = false;
        [SerializeField] private int targetFrameRate = 60;
        [SerializeField] private bool neverSleep = true;
        
        [Header("Debug Settings")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private KeyCode debugToggleKey = KeyCode.F1;
        
        // Singleton instance
        public static GameManager Instance { get; private set; }
        
        // Game state
        private bool isPaused = false;
        private bool isGameActive = true;
        
        // Components
        private UI.PlayerHUD playerHUD;
        private UI.MobileInputUI mobileInputUI;
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            FindUIComponents();
            SetupMobileSettings();
        }
        
        private void Update()
        {
            HandleDebugInput();
            HandlePauseInput();
        }
        
        private void InitializeGame()
        {
            // Set target frame rate for mobile
            Application.targetFrameRate = targetFrameRate;
            
            // Prevent screen from sleeping on mobile
            if (neverSleep)
            {
                Screen.sleepTimeout = SleepTimeout.NeverSleep;
            }
            
            // Set initial time scale
            Time.timeScale = timeScale;
        }
        
        private void FindUIComponents()
        {
            // Find UI components in the scene
            playerHUD = FindObjectOfType<UI.PlayerHUD>();
            mobileInputUI = FindObjectOfType<UI.MobileInputUI>();
        }
        
        private void SetupMobileSettings()
        {
            bool isMobile = Application.isMobilePlatform || forceMobileUI;
            
            // Show/hide mobile UI based on platform
            if (mobileInputUI != null)
            {
                mobileInputUI.SetUIVisibility(isMobile);
            }
            
            // Adjust UI scale for different screen sizes
            AdjustUIForScreenSize();
        }
        
        private void AdjustUIForScreenSize()
        {
            // Get the main canvas
            Canvas mainCanvas = FindObjectOfType<Canvas>();
            if (mainCanvas == null) return;
            
            CanvasScaler scaler = mainCanvas.GetComponent<CanvasScaler>();
            if (scaler == null) return;
            
            // Adjust UI scaling based on screen resolution
            float screenRatio = (float)Screen.width / Screen.height;
            
            if (screenRatio < 1.5f) // Tall screens (like 4:3 or similar)
            {
                scaler.matchWidthOrHeight = 1f; // Match height
            }
            else if (screenRatio > 2f) // Very wide screens
            {
                scaler.matchWidthOrHeight = 0f; // Match width
            }
            else // Standard screens (16:9, 16:10, etc.)
            {
                scaler.matchWidthOrHeight = 0.5f; // Balance between width and height
            }
        }
        
        private void HandleDebugInput()
        {
            if (Input.GetKeyDown(debugToggleKey))
            {
                ToggleDebugInfo();
            }
        }
        
        private void HandlePauseInput()
        {
            // Handle pause input (ESC key or back button on mobile)
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (isPaused)
                    ResumeGame();
                else
                    PauseGame();
            }
        }
        
        public void PauseGame()
        {
            if (isPaused) return;
            
            isPaused = true;
            Time.timeScale = 0f;
            
            // Show pause menu (if implemented)
            // PauseMenu.Instance?.Show();
            
            Debug.Log("Game Paused");
        }
        
        public void ResumeGame()
        {
            if (!isPaused) return;
            
            isPaused = false;
            Time.timeScale = timeScale;
            
            // Hide pause menu (if implemented)
            // PauseMenu.Instance?.Hide();
            
            Debug.Log("Game Resumed");
        }
        
        public void RestartLevel()
        {
            Time.timeScale = 1f;
            SceneManager.LoadScene(SceneManager.GetActiveScene().name);
        }
        
        public void LoadScene(string sceneName)
        {
            Time.timeScale = 1f;
            SceneManager.LoadScene(sceneName);
        }
        
        public void QuitGame()
        {
            #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
            #else
            Application.Quit();
            #endif
        }
        
        private void ToggleDebugInfo()
        {
            showDebugInfo = !showDebugInfo;
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (pauseOnFocusLoss && !hasFocus && isGameActive)
            {
                PauseGame();
            }
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseOnFocusLoss && pauseStatus && isGameActive)
            {
                PauseGame();
            }
        }
        
        // Public getters
        public bool IsPaused => isPaused;
        public bool IsGameActive => isGameActive;
        public bool IsMobile => Application.isMobilePlatform || forceMobileUI;
        
        // Debug GUI
        private void OnGUI()
        {
            if (!showDebugInfo) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label($"FPS: {1f / Time.unscaledDeltaTime:F1}");
            GUILayout.Label($"Time Scale: {Time.timeScale}");
            GUILayout.Label($"Screen: {Screen.width}x{Screen.height}");
            GUILayout.Label($"Platform: {Application.platform}");
            GUILayout.Label($"Mobile UI: {(mobileInputUI != null && mobileInputUI.gameObject.activeInHierarchy)}");
            
            if (GUILayout.Button("Restart Level"))
            {
                RestartLevel();
            }
            
            if (GUILayout.Button(isPaused ? "Resume" : "Pause"))
            {
                if (isPaused)
                    ResumeGame();
                else
                    PauseGame();
            }
            
            GUILayout.EndArea();
        }
        
        // Events for other systems to subscribe to
        public System.Action OnGamePaused;
        public System.Action OnGameResumed;
        public System.Action OnGameRestarted;
        
        private void OnDestroy()
        {
            // Reset time scale when destroyed
            Time.timeScale = 1f;
            
            // Reset screen sleep timeout
            Screen.sleepTimeout = SleepTimeout.SystemSetting;
        }
    }
}
