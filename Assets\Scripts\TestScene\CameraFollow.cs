using UnityEngine;

namespace SoulsLike2D.TestScene
{
    public class CameraFollow : Mono<PERSON>ehaviour
    {
        [Header("Target Settings")]
        public Transform target;
        [SerializeField] private Vector3 offset = new Vector3(0, 2, -10);
        
        [Header("Follow Settings")]
        [SerializeField] private float followSpeed = 5f;
        [SerializeField] private float lookAheadDistance = 2f;
        [SerializeField] private float lookAheadSpeed = 2f;
        
        [Header("Boundaries")]
        [SerializeField] private bool useBoundaries = false;
        [SerializeField] private Vector2 minBounds = new Vector2(-20, -5);
        [SerializeField] private Vector2 maxBounds = new Vector2(20, 10);
        
        [Header("Smoothing")]
        [SerializeField] private bool smoothFollow = true;
        [SerializeField] private float smoothTime = 0.3f;
        
        // Private variables
        private Vector3 velocity = Vector3.zero;
        private Vector3 targetPosition;
        private float currentLookAhead = 0f;
        private Rigidbody2D targetRigidbody;
        
        private void Start()
        {
            // Auto-find player if target is not set
            if (target == null)
            {
                GameObject player = GameObject.FindGameObjectWithTag("Player");
                if (player != null)
                {
                    target = player.transform;
                }
            }
            
            // Get target rigidbody for look-ahead
            if (target != null)
            {
                targetRigidbody = target.GetComponent<Rigidbody2D>();
            }
            
            // Set initial position
            if (target != null)
            {
                transform.position = target.position + offset;
            }
        }
        
        private void LateUpdate()
        {
            if (target == null) return;
            
            FollowTarget();
        }
        
        private void FollowTarget()
        {
            // Calculate look-ahead based on player velocity
            float targetLookAhead = 0f;
            if (targetRigidbody != null)
            {
                targetLookAhead = targetRigidbody.linearVelocity.x * lookAheadDistance;
                targetLookAhead = Mathf.Clamp(targetLookAhead, -lookAheadDistance, lookAheadDistance);
            }
            
            // Smooth look-ahead transition
            currentLookAhead = Mathf.Lerp(currentLookAhead, targetLookAhead, lookAheadSpeed * Time.deltaTime);
            
            // Calculate target position
            targetPosition = target.position + offset;
            targetPosition.x += currentLookAhead;
            
            // Apply boundaries if enabled
            if (useBoundaries)
            {
                targetPosition.x = Mathf.Clamp(targetPosition.x, minBounds.x, maxBounds.x);
                targetPosition.y = Mathf.Clamp(targetPosition.y, minBounds.y, maxBounds.y);
            }
            
            // Move camera
            if (smoothFollow)
            {
                transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, smoothTime);
            }
            else
            {
                transform.position = Vector3.Lerp(transform.position, targetPosition, followSpeed * Time.deltaTime);
            }
        }
        
        // Public methods for external control
        public void SetTarget(Transform newTarget)
        {
            target = newTarget;
            if (target != null)
            {
                targetRigidbody = target.GetComponent<Rigidbody2D>();
            }
        }
        
        public void SetOffset(Vector3 newOffset)
        {
            offset = newOffset;
        }
        
        public void SetBoundaries(Vector2 min, Vector2 max)
        {
            minBounds = min;
            maxBounds = max;
            useBoundaries = true;
        }
        
        public void DisableBoundaries()
        {
            useBoundaries = false;
        }
        
        // Shake effect for impact feedback
        public void Shake(float intensity = 1f, float duration = 0.2f)
        {
            StartCoroutine(ShakeCoroutine(intensity, duration));
        }
        
        private System.Collections.IEnumerator ShakeCoroutine(float intensity, float duration)
        {
            Vector3 originalOffset = offset;
            float elapsed = 0f;
            
            while (elapsed < duration)
            {
                float x = Random.Range(-1f, 1f) * intensity;
                float y = Random.Range(-1f, 1f) * intensity;
                
                offset = originalOffset + new Vector3(x, y, 0);
                
                elapsed += Time.deltaTime;
                yield return null;
            }
            
            offset = originalOffset;
        }
        
        // Debug visualization
        private void OnDrawGizmosSelected()
        {
            if (target == null) return;
            
            // Draw follow line
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(transform.position, target.position);
            
            // Draw look-ahead indicator
            Gizmos.color = Color.cyan;
            Vector3 lookAheadPos = target.position + Vector3.right * currentLookAhead;
            Gizmos.DrawWireSphere(lookAheadPos, 0.5f);
            
            // Draw boundaries
            if (useBoundaries)
            {
                Gizmos.color = Color.red;
                Vector3 center = new Vector3((minBounds.x + maxBounds.x) / 2, (minBounds.y + maxBounds.y) / 2, 0);
                Vector3 size = new Vector3(maxBounds.x - minBounds.x, maxBounds.y - minBounds.y, 1);
                Gizmos.DrawWireCube(center, size);
            }
        }
    }
}
