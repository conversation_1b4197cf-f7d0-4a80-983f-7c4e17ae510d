using UnityEngine;
using UnityEngine.InputSystem;

namespace SoulsLike2D.TestScene
{
    public class TestSceneSetup : MonoBehavi<PERSON>
    {
        [Header("Player Setup")]
        [SerializeField] private GameObject playerPrefab;
        [SerializeField] private Vector3 playerSpawnPosition = new Vector3(0, 2, 0);
        
        [Header("Test Environment")]
        [SerializeField] private GameObject groundPrefab;
        [SerializeField] private GameObject platformPrefab;
        [SerializeField] private Material groundMaterial;
        [SerializeField] private Material platformMaterial;
        
        [Header("UI Setup")]
        [SerializeField] private Canvas uiCanvas;
        [SerializeField] private GameObject hudPrefab;
        [SerializeField] private GameObject mobileUIPrefab;
        
        [Header("Camera Setup")]
        [SerializeField] private Camera mainCamera;
        [SerializeField] private Vector3 cameraOffset = new Vector3(0, 2, -10);
        [SerializeField] private float cameraFollowSpeed = 5f;
        
        private GameObject playerInstance;
        private Transform playerTransform;
        
        private void Start()
        {
            SetupTestScene();
        }
        
        private void SetupTestScene()
        {
            CreateGround();
            CreatePlatforms();
            CreatePlayer();
            SetupCamera();
            SetupUI();
            
            Debug.Log("Test Scene Setup Complete!");
            Debug.Log("Controls:");
            Debug.Log("- WASD/Arrow Keys: Move");
            Debug.Log("- Space: Jump");
            Debug.Log("- Left Click: Attack");
            Debug.Log("- Hold Left Click: Heavy Attack");
            Debug.Log("- Right Click: Block");
            Debug.Log("- Left Shift: Dodge");
            Debug.Log("- F1: Toggle Debug Info");
        }
        
        private void CreateGround()
        {
            // Main ground platform
            GameObject ground = CreatePlatform("Ground", new Vector3(0, -2, 0), new Vector3(20, 1, 1));
            if (groundMaterial != null)
                ground.GetComponent<Renderer>().material = groundMaterial;
            
            // Additional ground pieces
            CreatePlatform("Ground_Left", new Vector3(-15, -2, 0), new Vector3(10, 1, 1));
            CreatePlatform("Ground_Right", new Vector3(15, -2, 0), new Vector3(10, 1, 1));
        }
        
        private void CreatePlatforms()
        {
            // Jumping platforms
            CreatePlatform("Platform_1", new Vector3(-8, 0, 0), new Vector3(3, 0.5f, 1));
            CreatePlatform("Platform_2", new Vector3(-4, 2, 0), new Vector3(3, 0.5f, 1));
            CreatePlatform("Platform_3", new Vector3(0, 4, 0), new Vector3(3, 0.5f, 1));
            CreatePlatform("Platform_4", new Vector3(4, 2, 0), new Vector3(3, 0.5f, 1));
            CreatePlatform("Platform_5", new Vector3(8, 0, 0), new Vector3(3, 0.5f, 1));
            
            // High platform for testing
            CreatePlatform("High_Platform", new Vector3(12, 6, 0), new Vector3(4, 0.5f, 1));
        }
        
        private GameObject CreatePlatform(string name, Vector3 position, Vector3 scale)
        {
            GameObject platform = GameObject.CreatePrimitive(PrimitiveType.Cube);
            platform.name = name;
            platform.transform.position = position;
            platform.transform.localScale = scale;
            
            // Set layer to Ground
            platform.layer = LayerMask.NameToLayer("Default");
            
            // Add physics material for better feel
            Collider platformCollider = platform.GetComponent<Collider>();
            if (platformCollider != null)
            {
                PhysicsMaterial physicMat = new PhysicsMaterial("Platform");
                physicMat.friction = 0.6f;
                physicMat.bounciness = 0f;
                platformCollider.material = physicMat;
            }
            
            // Color coding
            Renderer renderer = platform.GetComponent<Renderer>();
            if (name.Contains("Ground"))
                renderer.material.color = new Color(0.4f, 0.3f, 0.2f); // Brown for ground
            else
                renderer.material.color = new Color(0.6f, 0.6f, 0.6f); // Gray for platforms
            
            return platform;
        }
        
        private void CreatePlayer()
        {
            if (playerPrefab != null)
            {
                playerInstance = Instantiate(playerPrefab, playerSpawnPosition, Quaternion.identity);
            }
            else
            {
                // Create player from scratch if no prefab
                playerInstance = CreatePlayerFromScratch();
            }
            
            playerInstance.name = "Player";
            playerTransform = playerInstance.transform;
            
            // Ensure player has the correct tag
            playerInstance.tag = "Player";
        }
        
        private GameObject CreatePlayerFromScratch()
        {
            GameObject player = new GameObject("Player");
            player.transform.position = playerSpawnPosition;
            
            // Add visual representation
            GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Capsule);
            visual.name = "Visual";
            visual.transform.SetParent(player.transform);
            visual.transform.localPosition = Vector3.zero;
            visual.transform.localScale = new Vector3(0.8f, 1f, 0.8f);
            
            // Remove the collider from visual (we'll add our own)
            DestroyImmediate(visual.GetComponent<Collider>());
            
            // Set color
            visual.GetComponent<Renderer>().material.color = Color.blue;
            
            // Add physics components
            Rigidbody2D rb = player.AddComponent<Rigidbody2D>();
            rb.freezeRotation = true;
            rb.gravityScale = 3f;
            
            CapsuleCollider2D collider = player.AddComponent<CapsuleCollider2D>();
            collider.size = new Vector2(0.8f, 1.8f);
            
            // Add ground check
            GameObject groundCheck = new GameObject("GroundCheck");
            groundCheck.transform.SetParent(player.transform);
            groundCheck.transform.localPosition = new Vector3(0, -0.9f, 0);
            
            // Add Input System
            PlayerInput playerInput = player.AddComponent<PlayerInput>();
            playerInput.actions = Resources.Load<InputActionAsset>("InputSystem_Actions");
            
            // Add our scripts
            player.AddComponent<SoulsLike2D.Player.PlayerController2D>();
            player.AddComponent<SoulsLike2D.Player.PlayerHealth>();
            player.AddComponent<SoulsLike2D.Player.PlayerAnimationController>();
            
            return player;
        }
        
        private void SetupCamera()
        {
            if (mainCamera == null)
                mainCamera = Camera.main;
            
            if (mainCamera == null)
            {
                GameObject cameraObj = new GameObject("Main Camera");
                mainCamera = cameraObj.AddComponent<Camera>();
                cameraObj.tag = "MainCamera";
            }
            
            // Add camera follow script
            CameraFollow cameraFollow = mainCamera.gameObject.GetComponent<CameraFollow>();
            if (cameraFollow == null)
                cameraFollow = mainCamera.gameObject.AddComponent<CameraFollow>();
            
            cameraFollow.target = playerTransform;
            cameraFollow.offset = cameraOffset;
            cameraFollow.followSpeed = cameraFollowSpeed;
        }
        
        private void SetupUI()
        {
            if (uiCanvas == null)
            {
                // Create main UI canvas
                GameObject canvasObj = new GameObject("UI Canvas");
                uiCanvas = canvasObj.AddComponent<Canvas>();
                uiCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
                
                CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                scaler.matchWidthOrHeight = 0.5f;
                
                canvasObj.AddComponent<GraphicRaycaster>();
            }
            
            // Create HUD
            CreateHUD();
            
            // Create Mobile UI
            CreateMobileUI();
            
            // Add Game Manager
            if (FindObjectOfType<SoulsLike2D.Core.GameManager>() == null)
            {
                GameObject gameManagerObj = new GameObject("Game Manager");
                gameManagerObj.AddComponent<SoulsLike2D.Core.GameManager>();
            }
        }
        
        private void CreateHUD()
        {
            GameObject hudObj = new GameObject("Player HUD");
            hudObj.transform.SetParent(uiCanvas.transform, false);
            
            // Create health bar
            CreateHealthBar(hudObj);
            
            // Create stamina bar
            CreateStaminaBar(hudObj);
            
            // Add HUD script
            hudObj.AddComponent<SoulsLike2D.UI.PlayerHUD>();
        }
        
        private void CreateHealthBar(GameObject parent)
        {
            GameObject healthBarObj = new GameObject("Health Bar");
            healthBarObj.transform.SetParent(parent.transform, false);
            
            RectTransform rect = healthBarObj.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0.02f, 0.9f);
            rect.anchorMax = new Vector2(0.4f, 0.95f);
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;
            
            // Background
            Image bg = healthBarObj.AddComponent<Image>();
            bg.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
            
            // Fill
            GameObject fillObj = new GameObject("Fill");
            fillObj.transform.SetParent(healthBarObj.transform, false);
            
            RectTransform fillRect = fillObj.AddComponent<RectTransform>();
            fillRect.anchorMin = Vector2.zero;
            fillRect.anchorMax = Vector2.one;
            fillRect.offsetMin = Vector2.zero;
            fillRect.offsetMax = Vector2.zero;
            
            Image fillImage = fillObj.AddComponent<Image>();
            fillImage.color = Color.red;
            fillImage.type = Image.Type.Filled;
            
            // Slider component
            Slider slider = healthBarObj.AddComponent<Slider>();
            slider.fillRect = fillRect;
            slider.value = 1f;
        }
        
        private void CreateStaminaBar(GameObject parent)
        {
            GameObject staminaBarObj = new GameObject("Stamina Bar");
            staminaBarObj.transform.SetParent(parent.transform, false);
            
            RectTransform rect = staminaBarObj.AddComponent<RectTransform>();
            rect.anchorMin = new Vector2(0.02f, 0.83f);
            rect.anchorMax = new Vector2(0.4f, 0.88f);
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;
            
            // Background
            Image bg = staminaBarObj.AddComponent<Image>();
            bg.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
            
            // Fill
            GameObject fillObj = new GameObject("Fill");
            fillObj.transform.SetParent(staminaBarObj.transform, false);
            
            RectTransform fillRect = fillObj.AddComponent<RectTransform>();
            fillRect.anchorMin = Vector2.zero;
            fillRect.anchorMax = Vector2.one;
            fillRect.offsetMin = Vector2.zero;
            fillRect.offsetMax = Vector2.zero;
            
            Image fillImage = fillObj.AddComponent<Image>();
            fillImage.color = Color.green;
            fillImage.type = Image.Type.Filled;
            
            // Slider component
            Slider slider = staminaBarObj.AddComponent<Slider>();
            slider.fillRect = fillRect;
            slider.value = 1f;
        }
        
        private void CreateMobileUI()
        {
            GameObject mobileUIObj = new GameObject("Mobile UI");
            mobileUIObj.transform.SetParent(uiCanvas.transform, false);
            
            // This would create the mobile UI elements
            // For now, just add the script
            mobileUIObj.AddComponent<SoulsLike2D.UI.MobileInputUI>();
            
            // Hide on non-mobile platforms
            #if !UNITY_ANDROID && !UNITY_IOS
            mobileUIObj.SetActive(false);
            #endif
        }
    }
}
